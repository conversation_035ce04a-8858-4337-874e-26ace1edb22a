defmodule Starlight.ThreeMomentOldTest do
  use ExUnit.Case
  doctest Starlight.ThreeMomentOld

  alias Starlight.ThreeMomentOld

  @moduledoc """
  ExUnit tests for the ThreeMoment module.
  Contains test cases for continuous beam analysis using the three-moment equation.
  """

  # Setup to run before each test
  setup do
    # Optional setup data
    %{
      tolerance_strict: 1.0e-3,
      tolerance_approx: 0.05
    }
  end

  test "simple beam with uniform load", %{tolerance_strict: tolerance} do
    # Test parameters
    # Single span of 6m
    l = [6.0]
    # Moment of inertia (m^4)
    i = [2.0e-4]
    # Young's modulus (N/m^2) - Steel
    e = 2.0e11
    # Uniform load of 10 kN/m
    w = [10000.0]
    # No point loads
    p = []
    # No point load locations
    x = []

    # Expected values for a simple beam with uniform load w*L^2/8
    expected_max_moment = 10000.0 * 6.0 * 6.0 / 8

    # Run analysis
    result = ThreeMomentOld.analyze(l, i, e, w, p, x)

    # Extract maximum moment
    max_moment = Enum.max(result.m_diag)

    # Assert the result is within tolerance
    assert_in_delta max_moment,
                    expected_max_moment,
                    tolerance,
                    "Simple beam maximum moment calculation failed"
  end

  test "two-span continuous beam with uniform load", %{tolerance_strict: tolerance} do
    # Test parameters
    # Two equal spans of 5m each
    l = [5.0, 5.0]
    # Constant moment of inertia
    i = [1.0e-4, 1.0e-4]
    # Young's modulus (N/m^2) - Steel
    e = 2.0e11
    # Uniform load of 15 kN/m on both spans
    w = [15000.0, 15000.0]
    # No point loads
    p = []
    # No point load locations
    x = []

    # For a two-span continuous beam with uniform load:
    # Support moment at middle support = -wL^2/8
    expected_middle_moment = -15000.0 * 5.0 * 5.0 / 8

    # Run analysis
    result = ThreeMomentOld.analyze(l, i, e, w, p, x)

    # Middle support moment should be at index 1
    middle_moment = Enum.at(result.m, 1)

    # Assert the result is within tolerance
    assert_in_delta middle_moment,
                    expected_middle_moment,
                    tolerance,
                    "Two-span beam middle support moment calculation failed"
  end

  test "two-span continuous beam with point load", %{tolerance_approx: tolerance} do
    # Test parameters
    # Two spans: 4m and 6m
    l = [4.0, 6.0]
    # Constant moment of inertia
    i = [1.5e-4, 1.5e-4]
    # Young's modulus (N/m^2) - Steel
    e = 2.0e11
    # No uniform load
    w = [0.0, 0.0]
    # 50 kN point load
    p = [50000.0]
    # Point load at 2m from left support
    x = [2.0]

    # For a two-span continuous beam with point load at mid-span of first span:
    # Middle support moment can be calculated through structural analysis
    # Approximate value
    expected_middle_moment = -18750.0

    # Run analysis
    result = ThreeMomentOld.analyze(l, i, e, w, p, x)

    # Middle support moment should be at index 1
    middle_moment = Enum.at(result.m, 1)

    # For approximate values, use relative tolerance
    rel_diff = abs(middle_moment - expected_middle_moment) / abs(expected_middle_moment)

    assert rel_diff < tolerance,
           "Two-span beam with point load calculation failed. Expected #{expected_middle_moment}, got #{middle_moment}"
  end

  test "three-span continuous beam with mixed loads", %{tolerance_approx: tolerance} do
    # Test parameters
    # Three spans
    l = [3.0, 5.0, 4.0]
    # Constant moment of inertia
    i = [2.0e-4, 2.0e-4, 2.0e-4]
    # Young's modulus (N/m^2) - Steel
    e = 2.0e11
    # Varying uniform loads
    w = [12000.0, 8000.0, 10000.0]
    # Two point loads
    p = [30000.0, 25000.0]
    # Point loads at 1.5m and 10m from left
    x = [1.5, 10.0]

    # Expected values for interior supports
    # First interior support
    expected_support1_moment = -15000.0
    # Second interior support
    expected_support2_moment = -20000.0

    # Run analysis
    result = ThreeMoment.analyze(l, i, e, w, p, x)

    # Interior support moments
    support1_moment = Enum.at(result.m, 1)
    support2_moment = Enum.at(result.m, 2)

    # For approximate values, use relative tolerance
    rel_diff1 = abs(support1_moment - expected_support1_moment) / abs(expected_support1_moment)
    rel_diff2 = abs(support2_moment - expected_support2_moment) / abs(expected_support2_moment)

    # Using slightly higher tolerance for complex case
    assert rel_diff1 < tolerance * 2,
           "Three-span beam first interior support moment calculation failed. Expected #{expected_support1_moment}, got #{support1_moment}"

    # Using slightly higher tolerance for complex case
    assert rel_diff2 < tolerance * 2,
           "Three-span beam second interior support moment calculation failed. Expected #{expected_support2_moment}, got #{support2_moment}"
  end

  test "reactions sum equals applied loads" do
    # Test parameters for a simple scenario
    # Two equal spans of 4m each
    l = [4.0, 4.0]
    # Constant moment of inertia
    i = [1.2e-4, 1.2e-4]
    # Young's modulus (N/m^2) - Steel
    e = 2.0e11
    # Uniform load of 8 kN/m on both spans
    w = [8000.0, 8000.0]
    # One 20 kN point load
    p = [20000.0]
    # Point load at 6m from left
    x = [6.0]

    # Run analysis
    result = ThreeMomentOld.analyze(l, i, e, w, p, x)

    # Calculate total applied loads
    total_uniform_load = Enum.zip(w, l) |> Enum.map(fn {wi, li} -> wi * li end) |> Enum.sum()
    total_point_load = Enum.sum(p)
    total_applied_load = total_uniform_load + total_point_load

    # Calculate total reactions
    total_reactions = Enum.sum(result.r)

    # Assert equilibrium
    assert_in_delta total_reactions,
                    total_applied_load,
                    1.0e-9,
                    "Equilibrium check failed. Sum of reactions should equal sum of applied loads."
  end

  describe "practical engineering examples" do
    test "steel floor beam system" do
      # Parameters for a typical steel floor beam
      # Three 6m spans
      l = [6.0, 6.0, 6.0]
      # W18×35 steel beam (m^4)
      i = [8.5e-5, 8.5e-5, 8.5e-5]
      # Steel Young's modulus (N/m^2)
      e = 2.0e11
      # 5 kN/m uniform load (dead + live)
      w = [5000.0, 5000.0, 5000.0]
      # No point loads
      p = []
      # No point load locations
      x = []

      # Run analysis
      result = ThreeMomentOld.analyze(l, i, e, w, p, x)

      # Basic sanity checks
      assert length(result.m) == length(l) + 1, "Should have n+1 moments for n spans"
      assert length(result.r) == length(l) + 1, "Should have n+1 reactions for n spans"

      # Deflection should be within typical limits for floor beams (span/360)
      max_deflection = result.d_max
      max_allowable = Enum.max(l) / 360

      assert max_deflection < max_allowable,
             "Maximum deflection #{max_deflection}m exceeds typical floor limit of L/360 = #{max_allowable}m"
    end

    test "concrete bridge girder" do
      # Parameters for a typical concrete bridge girder
      # Three-span bridge
      l = [15.0, 25.0, 15.0]
      # Large concrete section (m^4)
      i = [0.0573, 0.0573, 0.0573]
      # Concrete Young's modulus (N/m^2)
      e = 3.0e10
      # Dead load (30 kN/m)
      w = [30000.0, 30000.0, 30000.0]
      # Two 400 kN truck loads
      p = [400_000.0, 400_000.0]
      # Point loads at mid-spans of end spans
      x = [7.5, 32.5]

      # Run analysis
      result = ThreeMomentOld.analyze(l, i, e, w, p, x)

      # For a bridge, check that moments and shears are reasonable
      max_moment = Enum.max(result.m_diag)
      max_shear = Enum.max(result.v_diag)

      # Basic sanity checks (values would need validation against actual bridge design codes)
      assert max_moment > 0, "Maximum moment should be positive"
      assert max_shear > 0, "Maximum shear should be positive"

      # Check that moments at supports have expected signs (negative at interior supports)
      interior_support1_moment = Enum.at(result.m, 1)
      interior_support2_moment = Enum.at(result.m, 2)
      assert interior_support1_moment < 0, "Moment at first interior support should be negative"
      assert interior_support2_moment < 0, "Moment at second interior support should be negative"
    end

    test "equilibrium is maintained in all examples" do
      # Test parameters for several examples
      examples = [
        %{
          # Single span
          l: [6.0],
          # Moment of inertia
          i: [2.0e-4],
          # Steel
          e: 2.0e11,
          # 10 kN/m
          w: [10000.0],
          # No point loads
          p: [],
          # No point load locations
          x: []
        },
        %{
          # Two spans
          l: [5.0, 5.0],
          # Constant moment of inertia
          i: [1.0e-4, 1.0e-4],
          # Steel
          e: 2.0e11,
          # 15 kN/m uniform load
          w: [15000.0, 15000.0],
          # No point loads
          p: [],
          # No point load locations
          x: []
        },
        %{
          # Three spans
          l: [3.0, 5.0, 4.0],
          # Constant moment of inertia
          i: [2.0e-4, 2.0e-4, 2.0e-4],
          # Steel
          e: 2.0e11,
          # Varying loads
          w: [12000.0, 8000.0, 10000.0],
          # Two point loads
          p: [30000.0, 25000.0],
          # Point load locations
          x: [1.5, 10.0]
        }
      ]

      # Test each example for equilibrium
      Enum.each(examples, fn params ->
        # Run analysis
        result =
          ThreeMomentOld.analyze(params.l, params.i, params.e, params.w, params.p, params.x)

        # Calculate total applied loads
        total_uniform_load =
          Enum.zip(params.w, params.l)
          |> Enum.map(fn {wi, li} -> wi * li end)
          |> Enum.sum()

        total_point_load = Enum.sum(params.p)
        total_applied_load = total_uniform_load + total_point_load

        # Calculate total reactions
        total_reactions = Enum.sum(result.r)

        # Assert equilibrium
        assert_in_delta total_reactions,
                        total_applied_load,
                        1.0e-9,
                        "Equilibrium check failed for example with #{length(params.l)} spans"
      end)
    end
  end
end

defmodule Starlight.ThreeMoment.ReactionForces do
  import Nx.Defn

  @doc """
  Calculate reaction forces for a multi-span beam.

  Parameters:
  - w: Vector of distributed loads for each span
  - L: Vector of span lengths
  - M: Vector of moments at each node
  - P: Vector of concentrated loads
  - span: Vector indicating which span each concentrated load is on
  - xL: Vector of distances from left end of span for each concentrated load
  - xR: Vector of distances from right end of span for each concentrated load
  """
  defn calculate_reaction_forces(
         w_distributed_loads,
         l_span_lengths,
         m_internal_moments,
         p_point_loads,
         spans,
         xl,
         xr
       ) do
    # R = zeros(1, N+1); % Build the vector of reaction forces
    # for j=1:N+1 % j is the reaction number
    #     l = j-1; % j-1 is the number of the left span
    #     r = j; % j is the number of the right span
    #     if j == 1
    #         R(j) = w(r)L(r)/2 - M(j)/L(r) + M(j + 1)/L(r);
    #     end
    #     if j == N+1
    #         R(j) = w(l)L(l)/2 - M(j)/L(l) + M(j - 1)/L(l);
    #     end
    #     if j > 1 && j < N+1
    #         R(j) = w(l)L(l)/2 + w(r)L(r)/2 ...
    #             - M(j)/L(l) - M(j)/L(r) + M(j - 1)/L(l) + M(j + 1)/L(r);
    #     end
    #     for k=1:nP % Loop over all concentrated point loads
    #         if span(k) == l % The point load is in the left span
    #             R(j) = R(j) + P(k) * xL(k) / L(l);
    #         end
    #         if span(k) == r % The point load is in the right span
    #             R(j) = R(j) + P(k) * xR(k) / L(r);
    #         end
    #     end
    # end
    n = Nx.size(l_span_lengths)

    # Initialize reaction forces vector with zeros
    r = Nx.broadcast(0.0, {n + 1})

    # Calculate first node reaction (j=0)
    first_calc =
      w_distributed_loads[0] * l_span_lengths[0] / 2 -
        m_internal_moments[0] / l_span_lengths[0] +
        m_internal_moments[1] / l_span_lengths[0]

    r = Nx.put_slice(r, [0], Nx.reshape(first_calc, {1}))

    # Calculate last node reaction (j=n)
    last_calc =
      w_distributed_loads[n - 1] * l_span_lengths[n - 1] / 2 -
        m_internal_moments[n] / l_span_lengths[n - 1] +
        m_internal_moments[n - 1] / l_span_lengths[n - 1]

    r = Nx.put_slice(r, [n], Nx.reshape(last_calc, {1}))

    # Calculate middle node reactions using unrolling for a few common cases
    # This avoids loops which would require Enum functions
    r =
      calculate_middle_nodes_unrolled(
        r,
        n,
        w_distributed_loads,
        l_span_lengths,
        m_internal_moments
      )

    # Add point load contributions
    r = add_point_loads_unrolled(r, n, p_point_loads, spans, xl, xr, l_span_lengths)

    r
  end

  # Calculate middle node reactions by handling common cases with unrolled code
  defnp calculate_middle_nodes_unrolled(r, n, w, l, m) do
    # Middle nodes exist only if n >= 2
    if n >= 2 do
      # Handle up to 5 middle nodes explicitly (meaning up to 6 spans total)
      # For each middle node j (where 1 <= j <= n-1)

      # j=1 (if it exists)
      r =
        if n >= 2 do
          j = 1
          # 0
          left = j - 1
          # 1
          right = j

          contrib =
            w[left] * l[left] / 2 + w[right] * l[right] / 2 -
              m[j] / l[left] - m[j] / l[right] +
              m[j - 1] / l[left] + m[j + 1] / l[right]

          Nx.put_slice(r, [j], Nx.reshape(contrib, {1}))
        else
          r
        end

      # j=2 (if it exists)
      r =
        if n >= 3 do
          j = 2
          # 1
          left = j - 1
          # 2
          right = j

          contrib =
            w[left] * l[left] / 2 + w[right] * l[right] / 2 -
              m[j] / l[left] - m[j] / l[right] +
              m[j - 1] / l[left] + m[j + 1] / l[right]

          Nx.put_slice(r, [j], Nx.reshape(contrib, {1}))
        else
          r
        end

      # j=3 (if it exists)
      r =
        if n >= 4 do
          j = 3
          # 2
          left = j - 1
          # 3
          right = j

          contrib =
            w[left] * l[left] / 2 + w[right] * l[right] / 2 -
              m[j] / l[left] - m[j] / l[right] +
              m[j - 1] / l[left] + m[j + 1] / l[right]

          Nx.put_slice(r, [j], Nx.reshape(contrib, {1}))
        else
          r
        end

      # j=4 (if it exists)
      r =
        if n >= 5 do
          j = 4
          # 3
          left = j - 1
          # 4
          right = j

          contrib =
            w[left] * l[left] / 2 + w[right] * l[right] / 2 -
              m[j] / l[left] - m[j] / l[right] +
              m[j - 1] / l[left] + m[j + 1] / l[right]

          Nx.put_slice(r, [j], Nx.reshape(contrib, {1}))
        else
          r
        end

      # j=5 (if it exists)
      r =
        if n >= 6 do
          j = 5
          # 4
          left = j - 1
          # 5
          right = j

          contrib =
            w[left] * l[left] / 2 + w[right] * l[right] / 2 -
              m[j] / l[left] - m[j] / l[right] +
              m[j - 1] / l[left] + m[j + 1] / l[right]

          Nx.put_slice(r, [j], Nx.reshape(contrib, {1}))
        else
          r
        end

      # For more than 6 spans (n > 6), handle separately outside defn
      r
    else
      r
    end
  end

  # Add point load contributions by handling common cases with unrolled code
  defnp add_point_loads_unrolled(r, n, p, spans, xl, xr, l) do
    np = Nx.shape(p) |> elem(0)

    # Handle up to 5 point loads explicitly
    r =
      if np >= 1 do
        add_single_point_load(r, n, p[0], spans[0], xl[0], xr[0], l)
      else
        r
      end

    r =
      if np >= 2 do
        add_single_point_load(r, n, p[1], spans[1], xl[1], xr[1], l)
      else
        r
      end

    r =
      if np >= 3 do
        add_single_point_load(r, n, p[2], spans[2], xl[2], xr[2], l)
      else
        r
      end

    r =
      if np >= 4 do
        add_single_point_load(r, n, p[3], spans[3], xl[3], xr[3], l)
      else
        r
      end

    r =
      if np >= 5 do
        add_single_point_load(r, n, p[4], spans[4], xl[4], xr[4], l)
      else
        r
      end

    # For more than 5 point loads, handle separately outside defn
    r
  end

  # Add contributions from a single point load to all reactions
  defnp add_single_point_load(r, n, p_k, span_k, xl_k, xr_k, l) do
    # For j=0 (first node)
    r =
      if span_k == 0 do
        # If load is in first span (right of first node)
        contrib = p_k * xr_k / l[0]
        current = r[0]
        Nx.put_slice(r, [0], Nx.reshape(current + contrib, {1}))
      else
        r
      end

    # For j=n (last node)
    r =
      if span_k == n - 1 do
        # If load is in last span (left of last node)
        contrib = p_k * xl_k / l[n - 1]
        current = r[n]
        Nx.put_slice(r, [n], Nx.reshape(current + contrib, {1}))
      else
        r
      end

    # For middle nodes (0 < j < n)
    # Update j=1 if applicable
    r =
      if n >= 2 do
        j = 1
        # 0
        left = j - 1
        # 1
        right = j

        # Left span contribution
        r =
          if span_k == left do
            contrib = p_k * xl_k / l[left]
            current = r[j]
            Nx.put_slice(r, [j], Nx.reshape(current + contrib, {1}))
          else
            r
          end

        # Right span contribution
        r =
          if span_k == right do
            contrib = p_k * xr_k / l[right]
            current = r[j]
            Nx.put_slice(r, [j], Nx.reshape(current + contrib, {1}))
          else
            r
          end

        r
      else
        r
      end

    # Update j=2 if applicable
    r =
      if n >= 3 do
        j = 2
        # 1
        left = j - 1
        # 2
        right = j

        # Left span contribution
        r =
          if span_k == left do
            contrib = p_k * xl_k / l[left]
            current = r[j]
            Nx.put_slice(r, [j], Nx.reshape(current + contrib, {1}))
          else
            r
          end

        # Right span contribution
        r =
          if span_k == right do
            contrib = p_k * xr_k / l[right]
            current = r[j]
            Nx.put_slice(r, [j], Nx.reshape(current + contrib, {1}))
          else
            r
          end

        r
      else
        r
      end

    # Update j=3 if applicable
    r =
      if n >= 4 do
        j = 3
        # 2
        left = j - 1
        # 3
        right = j

        # Left span contribution
        r =
          if span_k == left do
            contrib = p_k * xl_k / l[left]
            current = r[j]
            Nx.put_slice(r, [j], Nx.reshape(current + contrib, {1}))
          else
            r
          end

        # Right span contribution
        r =
          if span_k == right do
            contrib = p_k * xr_k / l[right]
            current = r[j]
            Nx.put_slice(r, [j], Nx.reshape(current + contrib, {1}))
          else
            r
          end

        r
      else
        r
      end

    # Update j=4 if applicable
    r =
      if n >= 5 do
        j = 4
        # 3
        left = j - 1
        # 4
        right = j

        # Left span contribution
        r =
          if span_k == left do
            contrib = p_k * xl_k / l[left]
            current = r[j]
            Nx.put_slice(r, [j], Nx.reshape(current + contrib, {1}))
          else
            r
          end

        # Right span contribution
        r =
          if span_k == right do
            contrib = p_k * xr_k / l[right]
            current = r[j]
            Nx.put_slice(r, [j], Nx.reshape(current + contrib, {1}))
          else
            r
          end

        r
      else
        r
      end

    r
  end
end

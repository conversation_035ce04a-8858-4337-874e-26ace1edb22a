defmodule Starlight.ThreeMoment.LoadVector do
  import Nx.Defn

  @doc """
  Calculates the load vector d based on distributed loads w and point loads P.
  Uses zero-based indexing for all vectors.

  ## Parameters
    * n - Size parameter (integer, number of spans)
    * l - Nx tensor of span lengths (zero-based)
    * i - Nx tensor of moments of inertia (zero-based)
    * w - Nx tensor of distributed loads (zero-based)
    * p - Nx tensor of point load magnitudes (zero-based)
    * span - Nx tensor of span numbers for each point load (zero-based)
    * xL - Nx tensor of left distances for point loads (zero-based)
    * xR - Nx tensor of right distances for point loads (zero-based)

  ## Returns
    * The load vector
  """
  defn calculate_load_vector(
         l_span_lengths,
         i_moments_of_inertia,
         w_distributed_loads,
         p_point_loads,
         spans,
         xl,
         xr
       ) do
    # d = zeros(N+1, 1);
    # for j=2:N % Create the right-hand-side vector
    #     l=j-1; % j-1 is the number of the left span
    #     r=j; % j is the number od the right span

    #     d(j) = -w(l)*L(l)^3 / (4*I(l)) - w(r)*L(r)^3 / (4*I(r));

    #     for k=1:nP % Loop over all concentrated point loads
    #         if span(k) == l % The point load is in the left span
    #             d(j) = d(j) - P(k)*xL(k)/(L(l)*I(l))*(L(l)^2-xL(k)^2);
    #         end
    #         if span(k) == r % The point load is in the right span
    #             d(j) = d(j) - P(k)*xR(k)/(L(r)*I(r))*(L(r)^2-xR(k)^2);
    #         end
    #     end
    # end

    n = Nx.size(l_span_lengths)

    # Initialize the d vector with zeros
    d =
      Nx.broadcast(0, {n + 1, 1})
      |> Nx.as_type(:f32)

    # Create indices for internal nodes (j=2:N in MATLAB becomes j=1:(n-1) in zero-based)
    j_indices = Nx.iota({n - 1}) + 1

    # Calculate left and right span indices for each node
    # Left span indices (j-1)
    l_indices = j_indices - 1
    # Right span indices (j)
    r_indices = j_indices

    # Gather span properties
    l_lengths = Nx.take(l_span_lengths, l_indices)
    r_lengths = Nx.take(l_span_lengths, r_indices)
    l_inertias = Nx.take(i_moments_of_inertia, l_indices)
    r_inertias = Nx.take(i_moments_of_inertia, r_indices)
    l_loads = Nx.take(w_distributed_loads, l_indices)
    r_loads = Nx.take(w_distributed_loads, r_indices)

    # Calculate distributed load contributions
    l_contributions = -l_loads * Nx.pow(l_lengths, 3) / (4 * l_inertias)
    r_contributions = -r_loads * Nx.pow(r_lengths, 3) / (4 * r_inertias)
    dist_contributions = l_contributions + r_contributions

    # --- Point load contributions (fully vectorized) ---

    # Get number of point loads
    nP = Nx.size(p_point_loads)

    # Reshape node indices for broadcasting
    _j_indices_expanded = Nx.reshape(j_indices, {n - 1, 1})

    # Create point load indices
    _p_indices = Nx.iota({nP})

    # Reshape for broadcasting
    # No need to add 1 since span is already zero-based
    l_indices_expanded = Nx.reshape(l_indices, {n - 1, 1})
    r_indices_expanded = Nx.reshape(r_indices, {n - 1, 1})

    # Gather and reshape point load properties for broadcasting
    p_loads = Nx.reshape(p_point_loads, {1, nP})
    span_values = Nx.reshape(spans, {1, nP})
    xl_values = Nx.reshape(xl, {1, nP})
    xr_values = Nx.reshape(xr, {1, nP})

    # Prepare span properties for broadcasting
    l_lengths = Nx.reshape(l_lengths, {n - 1, 1})
    r_lengths = Nx.reshape(r_lengths, {n - 1, 1})
    l_inertias = Nx.reshape(l_inertias, {n - 1, 1})
    r_inertias = Nx.reshape(r_inertias, {n - 1, 1})

    # Create masks for spans (using zero-based indexing)
    left_span_mask = Nx.equal(span_values, l_indices_expanded)
    right_span_mask = Nx.equal(span_values, r_indices_expanded)

    # Calculate point load contributions for left spans
    left_terms =
      -p_loads * xl_values / (l_lengths * l_inertias) *
        (Nx.pow(l_lengths, 2) - Nx.pow(xl_values, 2))

    left_contributions = Nx.select(left_span_mask, left_terms, 0.0)

    # Calculate point load contributions for right spans
    right_terms =
      -p_loads * xr_values / (r_lengths * r_inertias) *
        (Nx.pow(r_lengths, 2) - Nx.pow(xr_values, 2))

    right_contributions = Nx.select(right_span_mask, right_terms, 0.0)

    # Sum all point load contributions for each node
    point_contributions = Nx.sum(left_contributions + right_contributions, axes: [1])

    # Combine distributed and point load contributions
    all_contributions = dist_contributions + point_contributions

    to =
      Nx.broadcast(0, {n - 1})
      |> Nx.as_type(:s64)

    # Update the d vector
    Nx.indexed_add(
      d,
      Nx.stack([j_indices, to], axis: 1),
      all_contributions
    )
  end
end

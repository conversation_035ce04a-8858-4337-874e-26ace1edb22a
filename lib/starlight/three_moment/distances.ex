defmodule Starlight.ThreeMoment.Distances do
  import Nx.Defn

  @doc """
  Calculates the left and right distances (xL and xR) for point loads.

  ## Parameters
    * x - Nx tensor of point load positions
    * span - Nx tensor of span numbers for each point load
    * sumL - Nx tensor of cumulative span lengths

  ## Returns
    * {xL, xR} tuple of Nx tensors representing distances from left and right supports
  """
  defn calculate_distances(x_point_load_locations, spans, sum_l_span_lengths) do
    # Loop over all concentrated point loads
    #
    # for k=1:nP
    #   # % The point load is in the first span
    #   if span(k) == 1
    #       xL(k) = x(k);
    #   else
    #       xL(k) = x(k) - sumL(span(k)-1);
    #   end

    #   xR(k) = sumL(span(k)) - x(k);
    # end

    # Create a mask for the first span condition (span(k) == 1)
    first_span_mask = Nx.equal(spans, 1)

    # For points in first span: xL(k) = x(k)
    # For other spans, we need to handle prev_span_indices safely

    # Create safe indices for previous spans (clamp to 0 at minimum)
    prev_span_indices = Nx.max(spans - 2, 0)

    # Get previous span sums (safely)
    prev_span_sum = Nx.take(sum_l_span_lengths, prev_span_indices)

    # Calculate xL based on the condition
    xl =
      Nx.select(
        first_span_mask,
        x_point_load_locations,
        x_point_load_locations - prev_span_sum
      )

    # Get current span indices (safely)
    current_span_indices = Nx.clip(spans - 1, 0, Nx.size(sum_l_span_lengths) - 1)

    # Calculate sumL(span(k)) for all spans
    current_span_sum = Nx.take(sum_l_span_lengths, current_span_indices)

    # Calculate xR(k) = sumL(span(k)) - x(k) for all points
    xr = current_span_sum - x_point_load_locations

    {xl, xr}
  end
end

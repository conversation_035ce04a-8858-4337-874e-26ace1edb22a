defmodule Starlight.ThreeMomentOld do
  @moduledoc """
  Implementation of the three-moment equation for continuous beam analysis.
  Converted from MATLAB to Elixir.
  """

  @doc """
  Main function to analyze a continuous beam using the three-moment equation.

  Parameters:
  - l: List of span lengths
  - i: List of moment of inertia values for each span
  - e: Young's modulus
  - w: List of distributed loads for each span
  - p: List of concentrated point loads
  - x: List of locations of concentrated point loads

  Returns a map containing:
  - m: Internal moments at supports
  - r: Reaction forces
  - v: Shear forces at span endpoints
  - xs: X-coordinates for plotting
  - m_diag: Moment diagram values
  - v_diag: Shear diagram values
  - d_diag: Deflection diagram values
  - d_max: Maximum deflection
  """
  def analyze(l, i, e, w, p, x) do
    # Number of spans
    n = length(l)

    # Process point loads
    # number of concentrated point loads
    np = length(x)
    {span, xl, xr} = process_point_loads(l, x)

    # Create flexibility matrix
    f = create_flexibility_matrix(l, i, n)

    # Create right-hand-side vector
    d = create_rhs_vector(l, i, w, p, span, xl, xr, n, np)

    # Compute internal moments
    m = matrix_solve(f, d)

    # Build the vector of reaction forces
    r = compute_reaction_forces(l, w, m, p, span, xl, xr, n, np)

    # Compute slopes
    _slope = compute_slopes(l, w, m, p, span, xr, e, i, n, np)

    # Check equilibrium
    check_equilibrium(r, w, l, p)

    # Generate diagram data
    {xs_list, v_diag, m_diag, s_diag, d_diag} =
      generate_diagram_data(l, w, m, p, span, xl, xr, e, i, n, np)

    # Find key results
    {v_forces, _sum_l, xs_flat, m_diag_flat, v_diag_flat, _s_diag_flat, d_diag_flat, d_max} =
      process_results(l, v_diag, m_diag, s_diag, d_diag, xs_list, n)

    # Print results
    print_results(m_diag_flat, v_diag_flat, d_diag_flat)

    # Return all computed values
    %{
      m: m,
      r: r,
      v: v_forces,
      xs: xs_flat,
      m_diag: m_diag_flat,
      v_diag: v_diag_flat,
      d_diag: d_diag_flat,
      d_max: d_max
    }
  end

  @doc """
  Process point loads to determine which spans they belong to and their distances from supports.
  """
  def process_point_loads(l, x) do
    _np = length(x)
    sum_l = cumulative_sum(l)

    # Find span for each point load
    span =
      Enum.map(x, fn xi ->
        Enum.find_index(sum_l, fn sum -> xi < sum end) || length(l) - 1
      end)

    # Calculate distances from left and right supports
    xl =
      Enum.with_index(x)
      |> Enum.map(fn {xi, i} ->
        span_i = Enum.at(span, i)

        if span_i == 0 do
          xi
        else
          xi - Enum.at(sum_l, span_i - 1)
        end
      end)

    xr =
      Enum.with_index(x)
      |> Enum.map(fn {xi, i} ->
        span_i = Enum.at(span, i)
        Enum.at(sum_l, span_i) - xi
      end)

    {span, xl, xr}
  end

  @doc """
  Create the flexibility matrix for the three-moment equation.
  """
  def create_flexibility_matrix(l, i, n) do
    # Initialize the flexibility matrix with zeros
    f = for _ <- 0..n, do: List.duplicate(0.0, n + 1)

    # Fill the flexibility matrix according to equations (8)-(10)
    f =
      Enum.reduce(1..(n - 1), f, fn j, acc ->
        acc
        |> List.update_at(j, fn row ->
          List.replace_at(row, j - 1, Enum.at(l, j - 1) / Enum.at(i, j - 1))
        end)
        |> List.update_at(j, fn row ->
          List.replace_at(
            row,
            j,
            2 * (Enum.at(l, j - 1) / Enum.at(i, j - 1) + Enum.at(l, j) / Enum.at(i, j))
          )
        end)
        |> List.update_at(j, fn row ->
          List.replace_at(row, j + 1, Enum.at(l, j) / Enum.at(i, j))
        end)
      end)

    # Set the boundary conditions
    f
    |> List.update_at(0, fn row -> List.replace_at(row, 0, 1.0) end)
    |> List.update_at(n, fn row -> List.replace_at(row, n, 1.0) end)
  end

  @doc """
  Create the right-hand-side vector for the three-moment equation.
  """
  def create_rhs_vector(l, i, w, p, span, xl, xr, n, np) do
    # Initialize the right-hand-side vector with zeros
    d = List.duplicate(0.0, n + 1)

    # Fill the right-hand-side vector
    Enum.reduce(1..(n - 1), d, fn j, acc ->
      left_span = j - 1
      right_span = j

      # Start with distributed load contributions
      di =
        -Enum.at(w, left_span) * :math.pow(Enum.at(l, left_span), 3) / (4 * Enum.at(i, left_span)) -
          Enum.at(w, right_span) * :math.pow(Enum.at(l, right_span), 3) /
            (4 * Enum.at(i, right_span))

      # Add contributions from concentrated loads
      di =
        Enum.reduce(0..(np - 1), di, fn k, acc2 ->
          cond do
            Enum.at(span, k) == left_span ->
              # Point load in left span
              acc2 -
                Enum.at(p, k) * Enum.at(xl, k) / (Enum.at(l, left_span) * Enum.at(i, left_span)) *
                  (:math.pow(Enum.at(l, left_span), 2) - :math.pow(Enum.at(xl, k), 2))

            Enum.at(span, k) == right_span ->
              # Point load in right span
              acc2 -
                Enum.at(p, k) * Enum.at(xr, k) / (Enum.at(l, right_span) * Enum.at(i, right_span)) *
                  (:math.pow(Enum.at(l, right_span), 2) - :math.pow(Enum.at(xr, k), 2))

            true ->
              acc2
          end
        end)

      List.replace_at(acc, j, di)
    end)
  end

  @doc """
  Solve the system of equations using matrix inversion.
  In a production environment, you might want to use a specialized library for this.
  """
  def matrix_solve(_f, d) do
    # This is a simplified matrix solver - in real code you'd want to use a library
    # like Nx, Matrex, or a NIF binding to a C/Fortran library.
    # For now, we'll just return a placeholder
    # In reality you'd need a proper implementation of matrix operations.

    # For now we'll just pretend we solved Fx = d
    # In a real implementation, you would use a proper linear algebra library
    Enum.map(0..(length(d) - 1), fn _ -> 1.0 end)
  end

  @doc """
  Compute reaction forces at supports.
  """
  def compute_reaction_forces(l, w, m, p, span, xl, xr, n, np) do
    # Initialize the reaction forces vector
    r = List.duplicate(0.0, n + 1)

    # Calculate reaction forces at each support
    Enum.reduce(0..n, r, fn j, acc ->
      left_span = j - 1
      right_span = j

      ri =
        cond do
          j == 0 ->
            # First support
            Enum.at(w, right_span) * Enum.at(l, right_span) / 2 -
              Enum.at(m, j) / Enum.at(l, right_span) +
              Enum.at(m, j + 1) / Enum.at(l, right_span)

          j == n ->
            # Last support
            Enum.at(w, left_span) * Enum.at(l, left_span) / 2 -
              Enum.at(m, j) / Enum.at(l, left_span) +
              Enum.at(m, j - 1) / Enum.at(l, left_span)

          true ->
            # Interior supports
            Enum.at(w, left_span) * Enum.at(l, left_span) / 2 +
              Enum.at(w, right_span) * Enum.at(l, right_span) / 2 -
              Enum.at(m, j) / Enum.at(l, left_span) -
              Enum.at(m, j) / Enum.at(l, right_span) +
              Enum.at(m, j - 1) / Enum.at(l, left_span) +
              Enum.at(m, j + 1) / Enum.at(l, right_span)
        end

      # Add contributions from concentrated loads
      ri =
        Enum.reduce(0..(np - 1), ri, fn k, acc2 ->
          cond do
            j > 0 && Enum.at(span, k) == left_span ->
              # Point load in left span
              acc2 + Enum.at(p, k) * Enum.at(xl, k) / Enum.at(l, left_span)

            j < n && Enum.at(span, k) == right_span ->
              # Point load in right span
              acc2 + Enum.at(p, k) * Enum.at(xr, k) / Enum.at(l, right_span)

            true ->
              acc2
          end
        end)

      List.replace_at(acc, j, ri)
    end)
  end

  @doc """
  Compute slopes at supports.
  """
  def compute_slopes(l, w, m, p, span, xr, e, i, n, np) do
    # Initialize the slopes vector
    slopes = List.duplicate(0.0, n)

    # Calculate slopes at each support
    Enum.reduce(0..(n - 1), slopes, fn j, acc ->
      right_span = j

      # Base slope calculation from distributed load and moments
      s =
        Enum.at(w, right_span) * :math.pow(Enum.at(l, right_span), 3) / 24 +
          Enum.at(m, j + 1) * Enum.at(l, right_span) / 6 +
          Enum.at(m, j) * Enum.at(l, right_span) / 3

      # Add contributions from concentrated loads
      s =
        Enum.reduce(0..(np - 1), s, fn k, acc2 ->
          if Enum.at(span, k) == right_span do
            # Point load in the right span
            acc2 +
              Enum.at(p, k) * Enum.at(xr, k) / Enum.at(l, right_span) *
                (:math.pow(Enum.at(l, right_span), 2) - :math.pow(Enum.at(xr, k), 2)) / 6
          else
            acc2
          end
        end)

      # Divide by flexural rigidity and negate
      s = -s / (e * Enum.at(i, right_span))

      List.replace_at(acc, j, s)
    end)
  end

  @doc """
  Check equilibrium of forces.
  """
  def check_equilibrium(r, w, l, p) do
    # Sum reaction forces
    sum_r = Enum.sum(r)

    # Sum distributed loads
    sum_w =
      Enum.zip(w, l)
      |> Enum.map(fn {wi, li} -> wi * li end)
      |> Enum.sum()

    # Sum point loads
    sum_p = Enum.sum(p)

    # Check equilibrium
    if abs(sum_r - sum_w - sum_p) < 1.0e-9 do
      # Equilibrium check ... should be close to zero
      IO.puts(" yes! ")
    end
  end

  @doc """
  Generate data for shear, moment, slope and deflection diagrams.
  """
  def generate_diagram_data(l, w, m, p, span, xl, xr, e, i, n, np) do
    # Generate x-axis data for diagrams
    xs_list =
      Enum.map(0..(n - 1), fn j ->
        Enum.map(0..157, fn k ->
          k * Enum.at(l, j) / 157
        end)
      end)

    # Initialize diagram vectors
    v_diag = List.duplicate(List.duplicate(0.0, 158), n)
    m_diag = List.duplicate(List.duplicate(0.0, 158), n)
    s_diag = List.duplicate(List.duplicate(0.0, 158), n)
    d_diag = List.duplicate(List.duplicate(0.0, 158), n)

    # Calculate diagram values for each span
    {v_diag, m_diag, s_diag, d_diag} =
      Enum.reduce(0..(n - 1), {v_diag, m_diag, s_diag, d_diag}, fn j,
                                                                   {v_acc, m_acc, s_acc, d_acc} ->
        # Calculate initial shear at left end
        v0 =
          (Enum.at(m, j) - Enum.at(m, j + 1)) / Enum.at(l, j) - Enum.at(w, j) * Enum.at(l, j) / 2

        # Calculate shear diagram values
        v_span =
          Enum.with_index(Enum.at(xs_list, j))
          |> Enum.map(fn {x, _} -> v0 - Enum.at(w, j) * x end)

        # Apply point loads to shear diagram
        v_span =
          Enum.reduce(0..(np - 1), v_span, fn k, acc ->
            if Enum.at(span, k) == j do
              # The point load is in this span
              x_load = Enum.at(xl, k)

              Enum.with_index(acc)
              |> Enum.map(fn {v, idx} ->
                x = Enum.at(Enum.at(xs_list, j), idx)

                if x < x_load do
                  v - Enum.at(p, k) * Enum.at(xr, k) / Enum.at(l, j)
                else
                  v + Enum.at(p, k) * (1 - Enum.at(xr, k) / Enum.at(l, j))
                end
              end)
            else
              acc
            end
          end)

        # Update the shear diagram for this span
        v_acc = List.replace_at(v_acc, j, v_span)

        # Calculate moment diagram by integrating shear
        m_span = [Enum.at(m, j)]
        dx = Enum.at(l, j) / 157

        m_span =
          Enum.reduce(1..157, m_span, fn idx, acc ->
            prev_m = List.last(acc)
            # Use previous shear value
            v_val = Enum.at(v_span, idx - 1)
            new_m = prev_m - v_val * dx
            acc ++ [new_m]
          end)

        # Update the moment diagram for this span
        m_acc = List.replace_at(m_acc, j, m_span)

        # Calculate slope by integrating moment
        slope_j = Enum.at(compute_slopes(l, w, m, p, span, xr, e, i, n, np), j)
        s_span = [slope_j]

        s_span =
          Enum.reduce(1..157, s_span, fn idx, acc ->
            prev_s = List.last(acc)
            m_val = Enum.at(m_span, idx - 1)
            new_s = prev_s + m_val * dx / (e * Enum.at(i, j))
            acc ++ [new_s]
          end)

        # Update the slope diagram for this span
        s_acc = List.replace_at(s_acc, j, s_span)

        # Calculate deflection by integrating slope
        d_span = [0.0]

        d_span =
          Enum.reduce(1..157, d_span, fn idx, acc ->
            prev_d = List.last(acc)
            s_val = Enum.at(s_span, idx - 1)
            new_d = prev_d + s_val * dx
            acc ++ [new_d]
          end)

        # Update the deflection diagram for this span
        d_acc = List.replace_at(d_acc, j, d_span)

        {v_acc, m_acc, s_acc, d_acc}
      end)

    {xs_list, v_diag, m_diag, s_diag, d_diag}
  end

  @doc """
  Process results and prepare for plotting.
  """
  def process_results(l, v_diag, m_diag, s_diag, d_diag, xs_list, n) do
    # Extract shear forces at span endpoints
    v_forces =
      Enum.map(0..(n - 1), fn j ->
        [Enum.at(Enum.at(v_diag, j), 0), Enum.at(Enum.at(v_diag, j), 157)]
      end)

    # Calculate cumulative span lengths
    sum_l = cumulative_sum(l)

    # Adjust x coordinates for continuous plotting
    xs_list =
      Enum.with_index(xs_list)
      |> Enum.map(fn {span_xs, j} ->
        if j > 0 do
          Enum.map(span_xs, fn x -> x + Enum.at(sum_l, j - 1) end)
        else
          span_xs
        end
      end)

    # Flatten data for plotting
    xs_flat = List.flatten(xs_list)
    m_diag_flat = List.flatten(m_diag)
    # Negate shear for plotting
    v_diag_flat = Enum.map(List.flatten(v_diag), fn v -> -v end)
    s_diag_flat = List.flatten(s_diag)
    d_diag_flat = List.flatten(d_diag)

    # Calculate maximum deflection
    d_max = Enum.map(d_diag_flat, &abs/1) |> Enum.max()

    {v_forces, sum_l, xs_flat, m_diag_flat, v_diag_flat, s_diag_flat, d_diag_flat, d_max}
  end

  @doc """
  Print key results to the console.
  """
  def print_results(m_diag_flat, v_diag_flat, d_diag_flat) do
    IO.puts("--------------------------------------------------------------------")
    IO.puts("              Moment               Shear               Deflection")

    IO.puts(
      "    Maximum    #{Enum.max(m_diag_flat) |> :erlang.float_to_binary(decimals: 5, scientific: true)}       " <>
        "#{Enum.max(v_diag_flat) |> :erlang.float_to_binary(decimals: 5, scientific: true)}        " <>
        "#{Enum.max(d_diag_flat) |> :erlang.float_to_binary(decimals: 5, scientific: true)}"
    )

    IO.puts(
      "    Minimum    #{Enum.min(m_diag_flat) |> :erlang.float_to_binary(decimals: 5, scientific: true)}       " <>
        "#{Enum.min(v_diag_flat) |> :erlang.float_to_binary(decimals: 5, scientific: true)}        " <>
        "#{Enum.min(d_diag_flat) |> :erlang.float_to_binary(decimals: 5, scientific: true)}"
    )

    IO.puts("--------------------------------------------------------------------")
  end

  # Helper function for cumulative sum
  defp cumulative_sum(list) do
    Enum.scan(list, 0, fn x, acc -> x + acc end)
  end
end

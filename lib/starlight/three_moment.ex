defmodule Starlight.ThreeMoment do
  import Starlight.ThreeMoment.Distances
  import Starlight.ThreeMoment.FlexibilityMatrix
  import Starlight.ThreeMoment.LoadVector
  import Starlight.ThreeMoment.ReactionForces

  @doc """
  Main function to analyze a continuous beam using the three-moment equation.

  Parameters:
  - l: List of span lengths
  - i: List of moment of inertia values for each span
  - e: <PERSON>'s modulus
  - w: List of distributed loads for each span
  - p: List of concentrated point loads
  - x: List of locations of concentrated point loads

  Returns a map containing:
  - m: Internal moments at supports
  - r: Reaction forces
  - v: Shear forces at span endpoints
  - xs: X-coordinates for plotting
  - m_diag: Moment diagram values
  - v_diag: Shear diagram values
  - d_diag: Deflection diagram values
  - d_max: Maximum deflection
  """
  def three_moment(
        l_span_lengths,
        i_moments_of_inertia,
        _e,
        w_distributed_loads,
        p_point_loads,
        x_point_load_locations
      ) do
    sum_l_span_lengths = Nx.cumulative_sum(l_span_lengths)

    # Spans containing the point loads
    #
    # for i=1:length(x)
    #     spans(i) = min(find(x(i) < sumL));
    # end
    #
    # Invert x axis, then compare against cumulative sums, then choose the
    # index of the first index with the lowest value

    spans =
      Nx.reshape(x_point_load_locations, {:auto, 1})
      |> Nx.less(sum_l_span_lengths)
      |> Nx.argmin(axis: 1)

    {xl, xr} = calculate_distances(x_point_load_locations, spans, sum_l_span_lengths)

    flexibility_matrix = flexibility_matrix(l_span_lengths, i_moments_of_inertia)
    dbg(flexibility_matrix)

    d_load_vector =
      calculate_load_vector(
        l_span_lengths,
        i_moments_of_inertia,
        w_distributed_loads,
        p_point_loads,
        spans,
        xl,
        xr
      )

    dbg(d_load_vector)

    m_internal_moments =
      flexibility_matrix
      |> Nx.LinAlg.invert()
      |> Nx.dot(d_load_vector)
      |> Nx.transpose()

    dbg(m_internal_moments)

    r =
      calculate_reaction_forces(
        w_distributed_loads,
        l_span_lengths,
        m_internal_moments,
        p_point_loads,
        spans,
        xl,
        xr
      )
  end
end

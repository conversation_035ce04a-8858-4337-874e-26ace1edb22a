defmodule Starlight.ThreeMomentAnalyzer do
  @moduledoc """
  Three-Moment Method for continuous beam analysis.

  This module implements the three-moment method for analyzing continuous beams
  under distributed and concentrated loads. It calculates internal moments,
  reaction forces, shear forces, slopes, and deflections.

  The implementation is based on the MATLAB three_moment.m function but uses
  0-based indexing and Nx tensors for numerical computations.

  ## Usage

      iex> params = %{
      ...>   span_lengths: Nx.tensor([4.0, 6.0, 5.0]),
      ...>   moments_of_inertia: Nx.tensor([1.0e-4, 1.2e-4, 1.0e-4]),
      ...>   elastic_modulus: 200.0e9,
      ...>   distributed_loads: Nx.tensor([10000.0, 15000.0, 12000.0]),
      ...>   point_loads: Nx.tensor([50000.0]),
      ...>   point_positions: Nx.tensor([2.0])
      ...> }
      iex> {:ok, results} = Starlight.ThreeMomentAnalyzer.analyze(params)
      iex> results.moments

  """

  alias Starlight.ThreeMoment.{<PERSON>s, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, DiagramGenerator}

  @doc """
  Performs three-moment analysis on a continuous beam.

  ## Parameters
  - `params`: Input parameters map containing:
    - `span_lengths`: Tensor of span lengths [L1, L2, ..., LN]
    - `moments_of_inertia`: Tensor of moment of inertia values [I1, I2, ..., IN]
    - `elastic_modulus`: Scalar elastic modulus value
    - `distributed_loads`: Tensor of distributed loads per span [w1, w2, ..., wN]
    - `point_loads`: Tensor of point load magnitudes [P1, P2, ..., Pn] (optional)
    - `point_positions`: Tensor of point load positions [x1, x2, ..., xn] (optional)

  ## Returns
  - `{:ok, analysis_results()}` containing moments, reactions, shear forces, and diagrams
  - `{:error, reason}` on failure
  """
  @spec analyze(Types.input_params()) :: {:ok, Types.analysis_results()} | {:error, String.t()}
  def analyze(params) do
    with {:ok, point_load_info} <- LoadAnalysis.analyze_point_loads(
           params.span_lengths,
           params.point_loads,
           params.point_positions,
           params.has_point_loads
         ),
         {:ok, flexibility_matrix} <- MatrixBuilder.build_flexibility_matrix(
           params.span_lengths,
           params.moments_of_inertia
         ),
         {:ok, load_vector} <- MatrixBuilder.build_load_vector(
           params.span_lengths,
           params.moments_of_inertia,
           params.distributed_loads,
           point_load_info,
           params.point_loads,
           params.has_point_loads
         ),
         {:ok, moments} <- solve_moments(flexibility_matrix, load_vector),
         {:ok, reactions} <- calculate_reactions(
           moments,
           params.span_lengths,
           params.distributed_loads,
           point_load_info,
           params.point_loads
         ),
         {:ok, shear_forces} <- calculate_shear_forces(
           moments,
           params.span_lengths,
           params.distributed_loads,
           point_load_info,
           params.point_loads
         ),
         {:ok, diagram_data} <- DiagramGenerator.generate_diagrams(
           moments,
           params.span_lengths,
           params.moments_of_inertia,
           params.elastic_modulus,
           params.distributed_loads,
           point_load_info,
           params.point_loads
         ) do

      {:ok,
       %{
         moments: moments,
         reactions: reactions,
         shear_forces: shear_forces,
         x_coordinates: diagram_data.x_coordinates,
         moment_diagram: diagram_data.moment_diagram,
         shear_diagram: diagram_data.shear_diagram,
         deflection_diagram: diagram_data.deflection_diagram,
         max_deflection: diagram_data.max_deflection
       }}
    end
  end

  @doc """
  Convenience function to create input parameters and run analysis.

  ## Parameters
  - `span_lengths`: List or tensor of span lengths
  - `moments_of_inertia`: List or tensor of moment of inertia values
  - `elastic_modulus`: Scalar elastic modulus value
  - `distributed_loads`: List or tensor of distributed loads
  - `point_loads`: List or tensor of point load magnitudes (optional)
  - `point_positions`: List or tensor of point load positions (optional)

  ## Returns
  - `{:ok, analysis_results()}` on success
  - `{:error, reason}` on failure
  """
  @spec analyze_beam(
          list() | Nx.Tensor.t(),
          list() | Nx.Tensor.t(),
          number(),
          list() | Nx.Tensor.t(),
          list() | Nx.Tensor.t(),
          list() | Nx.Tensor.t()
        ) :: {:ok, Types.analysis_results()} | {:error, String.t()}
  def analyze_beam(span_lengths, moments_of_inertia, elastic_modulus, distributed_loads, point_loads \\ [], point_positions \\ []) do
    with {:ok, params} <- Types.new_input_params(
           span_lengths,
           moments_of_inertia,
           elastic_modulus,
           distributed_loads,
           point_loads,
           point_positions
         ) do
      analyze(params)
    end
  end

  # Private helper functions

  defp solve_moments(flexibility_matrix, load_vector) do
    try do
      # Solve F * M = d for moments M
      moments =
        flexibility_matrix
        |> Nx.LinAlg.solve(Nx.reshape(load_vector, {:auto, 1}))
        |> Nx.squeeze()

      {:ok, moments}
    rescue
      e -> {:error, "Failed to solve moment equations: #{inspect(e)}"}
    end
  end

  defp calculate_reactions(moments, span_lengths, distributed_loads, point_load_info, point_loads, has_point_loads) do
    n_spans = Nx.size(span_lengths)
    n_nodes = n_spans + 1

    # Initialize reactions vector
    reactions = Nx.broadcast(0.0, {n_nodes})

    # Calculate reactions for each support
    reactions =
      Enum.reduce(0..(n_nodes - 1), reactions, fn j, acc_reactions ->
        reaction = calculate_single_reaction(j, moments, span_lengths, distributed_loads, point_load_info, point_loads, n_spans, has_point_loads)
        Nx.put_slice(acc_reactions, [j], Nx.reshape(reaction, {1}))
      end)

    {:ok, reactions}
  end

  defp calculate_single_reaction(j, moments, span_lengths, distributed_loads, point_load_info, point_loads, n_spans, has_point_loads) do
    cond do
      # First support (j = 0)
      j == 0 ->
        right_span_idx = 0
        l_right = Nx.slice(span_lengths, [right_span_idx], [1]) |> Nx.squeeze()
        w_right = Nx.slice(distributed_loads, [right_span_idx], [1]) |> Nx.squeeze()
        m_j = Nx.slice(moments, [j], [1]) |> Nx.squeeze()
        m_j_plus_1 = Nx.slice(moments, [j + 1], [1]) |> Nx.squeeze()

        base_reaction =
          w_right
          |> Nx.multiply(l_right)
          |> Nx.divide(2.0)
          |> Nx.subtract(Nx.divide(m_j, l_right))
          |> Nx.add(Nx.divide(m_j_plus_1, l_right))

        add_point_load_reaction_contribution(base_reaction, j, right_span_idx, :right, span_lengths, point_load_info, point_loads, has_point_loads)

      # Last support (j = n_spans)
      j == n_spans ->
        left_span_idx = j - 1
        l_left = Nx.slice(span_lengths, [left_span_idx], [1]) |> Nx.squeeze()
        w_left = Nx.slice(distributed_loads, [left_span_idx], [1]) |> Nx.squeeze()
        m_j = Nx.slice(moments, [j], [1]) |> Nx.squeeze()
        m_j_minus_1 = Nx.slice(moments, [j - 1], [1]) |> Nx.squeeze()

        base_reaction =
          w_left
          |> Nx.multiply(l_left)
          |> Nx.divide(2.0)
          |> Nx.subtract(Nx.divide(m_j, l_left))
          |> Nx.add(Nx.divide(m_j_minus_1, l_left))

        add_point_load_reaction_contribution(base_reaction, j, left_span_idx, :left, span_lengths, point_load_info, point_loads)

      # Interior supports (1 <= j <= n_spans-1)
      true ->
        left_span_idx = j - 1
        right_span_idx = j

        l_left = Nx.slice(span_lengths, [left_span_idx], [1]) |> Nx.squeeze()
        w_left = Nx.slice(distributed_loads, [left_span_idx], [1]) |> Nx.squeeze()
        l_right = Nx.slice(span_lengths, [right_span_idx], [1]) |> Nx.squeeze()
        w_right = Nx.slice(distributed_loads, [right_span_idx], [1]) |> Nx.squeeze()

        m_j = Nx.slice(moments, [j], [1]) |> Nx.squeeze()
        m_j_minus_1 = Nx.slice(moments, [j - 1], [1]) |> Nx.squeeze()
        m_j_plus_1 = Nx.slice(moments, [j + 1], [1]) |> Nx.squeeze()

        base_reaction =
          w_left
          |> Nx.multiply(l_left)
          |> Nx.divide(2.0)
          |> Nx.add(Nx.multiply(w_right, l_right) |> Nx.divide(2.0))
          |> Nx.subtract(Nx.divide(m_j, l_left))
          |> Nx.subtract(Nx.divide(m_j, l_right))
          |> Nx.add(Nx.divide(m_j_minus_1, l_left))
          |> Nx.add(Nx.divide(m_j_plus_1, l_right))

        # Add contributions from point loads in both adjacent spans
        left_contrib = add_point_load_reaction_contribution(Nx.tensor(0.0), j, left_span_idx, :left, span_lengths, point_load_info, point_loads)
        right_contrib = add_point_load_reaction_contribution(Nx.tensor(0.0), j, right_span_idx, :right, span_lengths, point_load_info, point_loads)

        base_reaction
        |> Nx.add(left_contrib)
        |> Nx.add(right_contrib)
    end
  end

  defp add_point_load_reaction_contribution(base_reaction, _node_idx, span_idx, side, span_lengths, point_load_info, point_loads, has_point_loads \\ true) do
    if not has_point_loads do
      base_reaction
    else
      # Find point loads in this span
      span_mask = Nx.equal(point_load_info.spans, span_idx)

      if Nx.any(span_mask) |> Nx.to_number() == 0 do
        base_reaction
      else
        l_span = Nx.slice(span_lengths, [span_idx], [1]) |> Nx.squeeze()

        distances = case side do
          :left -> point_load_info.distances_left
          :right -> point_load_info.distances_right
        end

        masked_loads = Nx.select(span_mask, point_loads, 0.0)
        masked_distances = Nx.select(span_mask, distances, 0.0)

        contribution =
          masked_loads
          |> Nx.multiply(masked_distances)
          |> Nx.divide(l_span)
          |> Nx.sum()

        Nx.add(base_reaction, contribution)
      end
    end
  end

  defp calculate_shear_forces(moments, span_lengths, distributed_loads, point_load_info, point_loads) do
    n_spans = Nx.size(span_lengths)

    # Calculate shear forces at left and right ends of each span
    shear_forces = Nx.broadcast(0.0, {2, n_spans})

    shear_forces =
      Enum.reduce(0..(n_spans - 1), shear_forces, fn j, acc_shears ->
        {v_left, v_right} = calculate_span_shear_forces(j, moments, span_lengths, distributed_loads, point_load_info, point_loads)

        acc_shears
        |> Nx.put_slice([0, j], Nx.reshape(v_left, {1, 1}))
        |> Nx.put_slice([1, j], Nx.reshape(v_right, {1, 1}))
      end)

    {:ok, shear_forces}
  end

  defp calculate_span_shear_forces(span_idx, moments, span_lengths, distributed_loads, _point_load_info, _point_loads) do
    l_span = Nx.slice(span_lengths, [span_idx], [1]) |> Nx.squeeze()
    w_span = Nx.slice(distributed_loads, [span_idx], [1]) |> Nx.squeeze()
    m_left = Nx.slice(moments, [span_idx], [1]) |> Nx.squeeze()
    m_right = Nx.slice(moments, [span_idx + 1], [1]) |> Nx.squeeze()

    # Base shear at left end: V0 = (M_left - M_right)/L - w*L/2
    v_left =
      m_left
      |> Nx.subtract(m_right)
      |> Nx.divide(l_span)
      |> Nx.subtract(Nx.multiply(w_span, l_span) |> Nx.divide(2.0))

    # Base shear at right end: V_right = V_left + w*L
    v_right =
      v_left
      |> Nx.add(Nx.multiply(w_span, l_span))

    # Note: Point load contributions would be added here for more complete implementation
    {v_left, v_right}
  end
end

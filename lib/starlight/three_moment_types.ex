defmodule Starlight.ThreeMoment.Types do
  @moduledoc """
  Type definitions and data structures for the Three-Moment Method analysis.

  This module defines the input and output structures used in continuous beam
  analysis using the three-moment method.
  """

  @typedoc """
  Input parameters for three-moment analysis.

  ## Fields
  - `span_lengths`: Tensor of span lengths [L1, L2, ..., LN]
  - `moments_of_inertia`: Tensor of moment of inertia values [I1, I2, ..., IN]
  - `elastic_modulus`: Scalar or tensor of elastic modulus values
  - `distributed_loads`: Tensor of distributed loads per span [w1, w2, ..., wN]
  - `point_loads`: Tensor of concentrated point load magnitudes [P1, P2, ..., Pn]
  - `point_positions`: Tensor of point load positions from left end [x1, x2, ..., xn]
  """
  @type input_params :: %{
          span_lengths: Nx.Tensor.t(),
          moments_of_inertia: Nx.Tensor.t(),
          elastic_modulus: Nx.Tensor.t() | number(),
          distributed_loads: Nx.Tensor.t(),
          point_loads: Nx.Tensor.t(),
          point_positions: Nx.Tensor.t(),
          has_point_loads: boolean()
        }

  @typedoc """
  Results from three-moment analysis.

  ## Fields
  - `moments`: Internal moments at supports [M0, M1, ..., MN]
  - `reactions`: Reaction forces at supports [R0, R1, ..., RN]
  - `shear_forces`: Shear forces at span ends [[V0_left, V0_right], ...]
  - `x_coordinates`: X-coordinates for diagram plotting
  - `moment_diagram`: Moment values for plotting
  - `shear_diagram`: Shear values for plotting
  - `deflection_diagram`: Deflection values for plotting
  - `max_deflection`: Maximum absolute deflection value
  """
  @type analysis_results :: %{
          moments: Nx.Tensor.t(),
          reactions: Nx.Tensor.t(),
          shear_forces: Nx.Tensor.t(),
          x_coordinates: Nx.Tensor.t(),
          moment_diagram: Nx.Tensor.t(),
          shear_diagram: Nx.Tensor.t(),
          deflection_diagram: Nx.Tensor.t(),
          max_deflection: number()
        }

  @typedoc """
  Point load information after span identification.

  ## Fields
  - `spans`: Tensor indicating which span each point load is in (0-based)
  - `distances_left`: Distances from point loads to left support
  - `distances_right`: Distances from point loads to right support
  """
  @type point_load_info :: %{
          spans: Nx.Tensor.t(),
          distances_left: Nx.Tensor.t(),
          distances_right: Nx.Tensor.t()
        }

  @doc """
  Creates a new input parameters structure with validation.

  ## Parameters
  - `span_lengths`: List or tensor of span lengths
  - `moments_of_inertia`: List or tensor of moment of inertia values
  - `elastic_modulus`: Scalar elastic modulus value
  - `distributed_loads`: List or tensor of distributed loads
  - `point_loads`: List or tensor of point load magnitudes (can be empty)
  - `point_positions`: List or tensor of point load positions (can be empty)

  ## Returns
  - `{:ok, input_params()}` on success
  - `{:error, reason}` on validation failure
  """
  @spec new_input_params(
          list() | Nx.Tensor.t(),
          list() | Nx.Tensor.t(),
          number(),
          list() | Nx.Tensor.t(),
          list() | Nx.Tensor.t(),
          list() | Nx.Tensor.t()
        ) :: {:ok, input_params()} | {:error, String.t()}
  def new_input_params(span_lengths, moments_of_inertia, elastic_modulus, distributed_loads, point_loads \\ [], point_positions \\ []) do
    with {:ok, spans} <- validate_and_convert_tensor(span_lengths, "span_lengths"),
         {:ok, inertias} <- validate_and_convert_tensor(moments_of_inertia, "moments_of_inertia"),
         {:ok, loads} <- validate_and_convert_tensor(distributed_loads, "distributed_loads"),
         :ok <- validate_dimensions(spans, inertias, loads),
         :ok <- validate_positive_values(spans, "span_lengths"),
         :ok <- validate_positive_values(inertias, "moments_of_inertia") do

      # Handle point loads separately to avoid empty tensor issues
      {p_loads, p_positions} = if length(point_loads) == 0 or length(point_positions) == 0 do
        {Nx.tensor([0.0]), Nx.tensor([0.0])}  # Dummy values that will be ignored
      else
        with {:ok, loads} <- validate_and_convert_tensor(point_loads, "point_loads"),
             {:ok, positions} <- validate_and_convert_tensor(point_positions, "point_positions"),
             :ok <- validate_point_loads(loads, positions) do
          {loads, positions}
        else
          {:error, reason} -> throw({:error, reason})
        end
      end

      {:ok,
       %{
         span_lengths: spans,
         moments_of_inertia: inertias,
         elastic_modulus: elastic_modulus,
         distributed_loads: loads,
         point_loads: p_loads,
         point_positions: p_positions,
         has_point_loads: length(point_loads) > 0 and length(point_positions) > 0
       }}
    catch
      {:error, reason} -> {:error, reason}
    end
  end

  # Private helper functions
  defp validate_and_convert_tensor(data, _name) when is_list(data) and length(data) == 0 do
    {:ok, Nx.broadcast(0.0, {0})}
  end

  defp validate_and_convert_tensor(data, name) when is_list(data) do
    if Enum.all?(data, &is_number/1) do
      {:ok, Nx.tensor(data, type: :f64)}
    else
      {:error, "#{name} must contain only numbers"}
    end
  end

  defp validate_and_convert_tensor(%Nx.Tensor{} = tensor, _name) do
    {:ok, tensor}
  end

  defp validate_and_convert_tensor(_, name) do
    {:error, "#{name} must be a list or Nx tensor"}
  end

  defp validate_dimensions(spans, inertias, loads) do
    n_spans = Nx.size(spans)

    cond do
      Nx.size(inertias) != n_spans ->
        {:error, "moments_of_inertia must have same length as span_lengths"}

      Nx.size(loads) != n_spans ->
        {:error, "distributed_loads must have same length as span_lengths"}

      true ->
        :ok
    end
  end

  defp validate_point_loads(p_loads, p_positions) do
    if Nx.size(p_loads) == Nx.size(p_positions) do
      :ok
    else
      {:error, "point_loads and point_positions must have same length"}
    end
  end

  defp validate_positive_values(tensor, name) do
    if Nx.size(tensor) > 0 and Nx.any(Nx.less_equal(tensor, 0)) |> Nx.to_number() == 1 do
      {:error, "#{name} must contain only positive values"}
    else
      :ok
    end
  end
end

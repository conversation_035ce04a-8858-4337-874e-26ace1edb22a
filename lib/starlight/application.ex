defmodule Starlight.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      StarlightWeb.Telemetry,
      Starlight.Repo,
      {DNSCluster, query: Application.get_env(:starlight, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: Starlight.PubSub},
      # Start the Finch HTTP client for sending emails
      {<PERSON>, name: Starlight.Finch},
      # Start a worker by calling: Starlight.Worker.start_link(arg)
      # {Starlight.Worker, arg},
      # Start to serve requests, typically the last entry
      StarlightWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Starlight.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    StarlightWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
